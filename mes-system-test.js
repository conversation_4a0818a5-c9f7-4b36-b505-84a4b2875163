// MES系统自动化测试脚本
const puppeteer = require('puppeteer');

class MESSystemTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.baseUrl = 'http://localhost:3000'; // 根据您的配置调整
    }

    async init() {
        console.log('初始化浏览器...');
        this.browser = await puppeteer.connect({
            browserWSEndpoint: 'ws://localhost:9222'
        });
        
        const pages = await this.browser.pages();
        this.page = pages[0] || await this.browser.newPage();
        
        // 设置视口
        await this.page.setViewport({ width: 1920, height: 1080 });
        
        console.log('✓ 浏览器连接成功');
    }

    async testHomePage() {
        console.log('测试首页加载...');
        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
            
            // 截图
            await this.page.screenshot({ path: 'mes-homepage.png' });
            
            // 检查页面标题
            const title = await this.page.title();
            console.log(`✓ 页面标题: ${title}`);
            
            // 检查关键元素
            const hasNavigation = await this.page.$('.ant-menu') !== null;
            const hasContent = await this.page.$('.ant-layout-content') !== null;
            
            console.log(`✓ 导航菜单: ${hasNavigation ? '存在' : '缺失'}`);
            console.log(`✓ 主要内容: ${hasContent ? '存在' : '缺失'}`);
            
            return true;
        } catch (error) {
            console.error('✗ 首页测试失败:', error.message);
            return false;
        }
    }

    async testLogin() {
        console.log('测试登录功能...');
        try {
            // 查找登录按钮或表单
            const loginButton = await this.page.$('[data-testid="login-button"], .login-button, button:contains("登录")');
            
            if (loginButton) {
                await loginButton.click();
                await this.page.waitForTimeout(1000);
                
                // 填写登录信息
                const usernameInput = await this.page.$('input[name="username"], input[placeholder*="用户名"]');
                const passwordInput = await this.page.$('input[name="password"], input[type="password"]');
                
                if (usernameInput && passwordInput) {
                    await usernameInput.type('admin');
                    await passwordInput.type('admin123');
                    
                    // 提交登录
                    const submitButton = await this.page.$('button[type="submit"], .ant-btn-primary');
                    if (submitButton) {
                        await submitButton.click();
                        await this.page.waitForTimeout(2000);
                    }
                }
                
                console.log('✓ 登录流程完成');
                return true;
            } else {
                console.log('ℹ 未找到登录按钮，可能已登录或无需登录');
                return true;
            }
        } catch (error) {
            console.error('✗ 登录测试失败:', error.message);
            return false;
        }
    }

    async testNavigation() {
        console.log('测试导航功能...');
        try {
            // 获取所有菜单项
            const menuItems = await this.page.$$('.ant-menu-item, .ant-menu-submenu');
            console.log(`发现 ${menuItems.length} 个菜单项`);
            
            for (let i = 0; i < Math.min(menuItems.length, 3); i++) {
                const menuItem = menuItems[i];
                const text = await menuItem.evaluate(el => el.textContent);
                console.log(`点击菜单: ${text}`);
                
                await menuItem.click();
                await this.page.waitForTimeout(1000);
                
                // 截图
                await this.page.screenshot({ path: `mes-menu-${i}.png` });
            }
            
            console.log('✓ 导航测试完成');
            return true;
        } catch (error) {
            console.error('✗ 导航测试失败:', error.message);
            return false;
        }
    }

    async testAPIEndpoints() {
        console.log('测试API端点...');
        try {
            const apiTests = [
                { url: '/api/health', name: '健康检查' },
                { url: '/api/users', name: '用户列表' },
                { url: '/api/production', name: '生产数据' },
                { url: '/api/equipment', name: '设备状态' }
            ];
            
            for (const test of apiTests) {
                try {
                    const response = await this.page.evaluate(async (url) => {
                        const res = await fetch(url);
                        return {
                            status: res.status,
                            ok: res.ok
                        };
                    }, test.url);
                    
                    console.log(`${test.name}: ${response.ok ? '✓' : '✗'} (${response.status})`);
                } catch (error) {
                    console.log(`${test.name}: ✗ (连接失败)`);
                }
            }
            
            return true;
        } catch (error) {
            console.error('✗ API测试失败:', error.message);
            return false;
        }
    }

    async testFormSubmission() {
        console.log('测试表单提交...');
        try {
            // 查找表单
            const forms = await this.page.$$('form, .ant-form');
            
            if (forms.length > 0) {
                const form = forms[0];
                
                // 填写表单字段
                const inputs = await form.$$('input[type="text"], input[type="number"], textarea');
                for (const input of inputs) {
                    const placeholder = await input.evaluate(el => el.placeholder);
                    if (placeholder) {
                        await input.clear();
                        await input.type('测试数据');
                    }
                }
                
                // 提交表单
                const submitButton = await form.$('button[type="submit"], .ant-btn-primary');
                if (submitButton) {
                    await submitButton.click();
                    await this.page.waitForTimeout(2000);
                }
                
                console.log('✓ 表单提交测试完成');
                return true;
            } else {
                console.log('ℹ 未找到表单');
                return true;
            }
        } catch (error) {
            console.error('✗ 表单测试失败:', error.message);
            return false;
        }
    }

    async generateReport() {
        console.log('生成测试报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            url: this.baseUrl,
            screenshots: [
                'mes-homepage.png',
                'mes-menu-0.png',
                'mes-menu-1.png',
                'mes-menu-2.png'
            ],
            summary: '测试完成'
        };
        
        require('fs').writeFileSync('mes-test-report.json', JSON.stringify(report, null, 2));
        console.log('✓ 测试报告已生成: mes-test-report.json');
    }

    async cleanup() {
        if (this.browser) {
            // 不关闭浏览器，保持连接
            console.log('✓ 测试完成，浏览器保持打开状态');
        }
    }

    async runAllTests() {
        try {
            await this.init();
            
            const tests = [
                this.testHomePage(),
                this.testLogin(),
                this.testNavigation(),
                this.testAPIEndpoints(),
                this.testFormSubmission()
            ];
            
            const results = await Promise.allSettled(tests);
            const passed = results.filter(r => r.status === 'fulfilled' && r.value).length;
            
            console.log(`\n=== 测试结果 ===`);
            console.log(`通过: ${passed}/${tests.length}`);
            
            await this.generateReport();
            
        } catch (error) {
            console.error('测试运行失败:', error.message);
        } finally {
            await this.cleanup();
        }
    }
}

// 运行测试
const tester = new MESSystemTester();
tester.runAllTests();
