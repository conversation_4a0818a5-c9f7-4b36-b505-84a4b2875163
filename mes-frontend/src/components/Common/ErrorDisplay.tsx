import React from 'react';
import { <PERSON><PERSON>, <PERSON>ton, Space, Typography } from 'antd';
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ErrorDisplayProps {
  error: Error | string | null;
  onRetry?: () => void;
  showRetry?: boolean;
  type?: 'error' | 'warning';
  style?: React.CSSProperties;
  showDetails?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  showRetry = true,
  type = 'error',
  style,
  showDetails = false,
}) => {
  if (!error) return null;

  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorStack = typeof error === 'object' && error.stack ? error.stack : null;

  const getErrorTitle = () => {
    if (errorMessage.includes('网络')) return '网络连接失败';
    if (errorMessage.includes('超时')) return '请求超时';
    if (errorMessage.includes('权限')) return '权限不足';
    if (errorMessage.includes('404')) return '资源不存在';
    if (errorMessage.includes('500')) return '服务器错误';
    return '操作失败';
  };

  const getErrorDescription = () => {
    if (errorMessage.includes('网络')) {
      return '请检查网络连接是否正常，或稍后重试';
    }
    if (errorMessage.includes('超时')) {
      return '服务器响应时间过长，请稍后重试';
    }
    if (errorMessage.includes('权限')) {
      return '您没有执行此操作的权限，请联系管理员';
    }
    if (errorMessage.includes('404')) {
      return '请求的资源不存在，请检查操作是否正确';
    }
    if (errorMessage.includes('500')) {
      return '服务器内部错误，请稍后重试或联系技术支持';
    }
    return errorMessage;
  };

  return (
    <div style={style}>
      <Alert
        message={getErrorTitle()}
        description={
          <div>
            <Text>{getErrorDescription()}</Text>
            {showRetry && onRetry && (
              <div style={{ marginTop: '12px' }}>
                <Button
                  type="primary"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={onRetry}
                >
                  重试
                </Button>
              </div>
            )}
            {showDetails && errorStack && process.env.NODE_ENV === 'development' && (
              <details style={{ marginTop: '12px' }}>
                <summary style={{ cursor: 'pointer', color: '#666' }}>
                  <Text type="secondary">查看详细错误信息</Text>
                </summary>
                <pre style={{
                  background: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  marginTop: '8px',
                  overflow: 'auto',
                  maxHeight: '200px',
                }}>
                  {errorStack}
                </pre>
              </details>
            )}
          </div>
        }
        type={type}
        icon={<ExclamationCircleOutlined />}
        showIcon
      />
    </div>
  );
};

export default ErrorDisplay;
