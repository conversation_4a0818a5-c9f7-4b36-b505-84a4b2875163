import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { authService } from '../services/auth';

// 布局组件
import MainLayout from '../components/Layout/MainLayout';
import AuthLayout from '../components/Layout/AuthLayout';

// 页面组件
import Login from '../pages/Auth/Login';
import Dashboard from '../pages/Dashboard';
import ProjectList from '../pages/Project/ProjectList';
import ProjectDetail from '../pages/Project/ProjectDetail';
import WorkOrderList from '../pages/WorkOrder/WorkOrderList';
import WorkOrderDetail from '../pages/WorkOrder/WorkOrderDetail';
import WorkOrderCreate from '../pages/WorkOrder/WorkOrderCreate';
import TaskList from '../pages/Task/TaskList';
import TaskDetail from '../pages/Task/TaskDetail';
import ExecutionBoard from '../pages/Execution/ExecutionBoard';
import MyTasks from '../pages/Execution/MyTasks';
import Analytics from '../pages/Analytics';
import UserManagement from '../pages/System/UserManagement';
import SystemSettings from '../pages/System/SystemSettings';

// 权限保护组件
interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, roles }) => {
  const isAuthenticated = authService.isAuthenticated();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (roles && roles.length > 0) {
    const hasPermission = authService.hasAnyRole(roles);
    if (!hasPermission) {
      return <Navigate to="/dashboard" replace />;
    }
  }
  
  return <>{children}</>;
};

// 公共路由组件
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticated();
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 公共路由 */}
      <Route path="/login" element={
        <PublicRoute>
          <AuthLayout>
            <Login />
          </AuthLayout>
        </PublicRoute>
      } />
      
      {/* 受保护的路由 */}
      <Route path="/" element={
        <ProtectedRoute>
          <MainLayout />
        </ProtectedRoute>
      }>
        {/* 仪表板 */}
        <Route index element={<Navigate to="/dashboard" replace />} />
        <Route path="dashboard" element={<Dashboard />} />
        
        {/* 项目管理 */}
        <Route path="projects" element={<ProjectList />} />
        <Route path="projects/:id" element={<ProjectDetail />} />
        
        {/* 工单管理 */}
        <Route path="work-orders" element={<WorkOrderList />} />
        <Route path="work-orders/create" element={<WorkOrderCreate />} />
        <Route path="work-orders/:id" element={<WorkOrderDetail />} />
        
        {/* 任务管理 */}
        <Route path="tasks" element={<TaskList />} />
        <Route path="tasks/:id" element={<TaskDetail />} />
        
        {/* 车间执行 */}
        <Route path="execution">
          <Route index element={<ExecutionBoard />} />
          <Route path="board" element={<ExecutionBoard />} />
          <Route path="my-tasks" element={<MyTasks />} />
        </Route>
        
        {/* 数据分析 */}
        <Route path="analytics" element={
          <ProtectedRoute roles={['ADMIN', 'MANAGER']}>
            <Analytics />
          </ProtectedRoute>
        } />
        
        {/* 系统管理 */}
        <Route path="system">
          <Route path="users" element={
            <ProtectedRoute roles={['ADMIN']}>
              <UserManagement />
            </ProtectedRoute>
          } />
          <Route path="settings" element={
            <ProtectedRoute roles={['ADMIN']}>
              <SystemSettings />
            </ProtectedRoute>
          } />
        </Route>
      </Route>
      
      {/* 404页面 */}
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
};

export default AppRoutes;
