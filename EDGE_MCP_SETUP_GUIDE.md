# Windows Edge MCP 安装调试指南

## 前提条件
- ✅ Windows 10/11
- ✅ Node.js 已安装
- ✅ Microsoft Edge 浏览器
- ✅ 管理员权限

## 第一步：运行安装脚本

1. **以管理员身份打开PowerShell**
2. **导航到项目目录**
   ```powershell
   cd C:\path\to\mes-system
   ```
3. **运行安装脚本**
   ```powershell
   .\windows-edge-mcp-setup.ps1
   ```

## 第二步：启动Edge调试模式

1. **关闭所有Edge窗口**
2. **运行调试启动脚本**
   ```cmd
   %USERPROFILE%\mcp-browser\start-edge-debug.bat
   ```
3. **验证调试端口**
   - 在新Edge窗口中访问：`http://localhost:9222`
   - 应该看到调试信息页面

## 第三步：测试MCP连接

1. **运行测试脚本**
   ```cmd
   cd %USERPROFILE%\mcp-browser
   node test-mcp-browser.js
   ```
2. **检查输出**
   - 应该显示"所有测试通过"

## 第四步：测试您的MES系统

1. **启动MES后端服务**（在Ubuntu服务器上）
   ```bash
   # 在您的Ubuntu服务器上
   cd /root/mes-system
   docker-compose up -d
   cargo run
   ```

2. **启动前端服务**
   ```bash
   cd mes-frontend
   npm start
   ```

3. **在Edge中访问MES系统**
   ```
   http://your-server-ip:3000
   ```

## 第五步：使用MCP工具

现在您可以使用MCP工具与Edge浏览器交互：

### 基本MCP命令示例
```javascript
// 导航到页面
browser.navigate("http://localhost:3000");

// 点击元素
browser.click("#login-button");

// 输入文本
browser.type("#username", "admin");

// 截图
browser.screenshot();
```

## 故障排除

### 问题1：端口9222被占用
```powershell
# 查找占用端口的进程
netstat -ano | findstr :9222
# 终止进程
taskkill /PID <进程ID> /F
```

### 问题2：Edge无法启动调试模式
- 确保完全关闭所有Edge进程
- 检查Edge安装路径是否正确
- 尝试手动运行命令：
  ```cmd
  "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" --remote-debugging-port=9222
  ```

### 问题3：MCP连接失败
- 检查Node.js版本（建议18+）
- 重新安装MCP包：
  ```cmd
  npm install @modelcontextprotocol/server-browser --force
  ```

### 问题4：无法访问Ubuntu服务器
- 检查防火墙设置
- 确保端口转发正确配置
- 验证服务器IP地址

## 下一步：集成测试

1. **自动化测试您的MES系统**
2. **创建测试脚本**
3. **设置持续集成**

## 需要帮助？
如果遇到问题，请提供：
1. 错误信息截图
2. PowerShell输出
3. Edge调试页面状态
4. 系统环境信息
