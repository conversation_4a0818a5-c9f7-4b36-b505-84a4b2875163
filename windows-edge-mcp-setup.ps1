# Windows Edge MCP 安装脚本
# 运行前请确保以管理员权限运行PowerShell

Write-Host "=== Windows Edge MCP 安装向导 ===" -ForegroundColor Green

# 检查Node.js安装
Write-Host "检查Node.js安装..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: npm未正确安装" -ForegroundColor Red
    exit 1
}

# 创建MCP工作目录
$mcpDir = "$env:USERPROFILE\mcp-browser"
Write-Host "创建MCP目录: $mcpDir" -ForegroundColor Yellow
if (!(Test-Path $mcpDir)) {
    New-Item -ItemType Directory -Path $mcpDir -Force
}
Set-Location $mcpDir

# 安装浏览器MCP服务器
Write-Host "安装浏览器MCP服务器..." -ForegroundColor Yellow
npm install @modelcontextprotocol/server-browser
npm install puppeteer
npm install playwright

# 创建MCP配置文件
$configContent = @"
{
  "mcpServers": {
    "browser": {
      "command": "node",
      "args": ["$mcpDir/node_modules/@modelcontextprotocol/server-browser/dist/index.js"],
      "env": {
        "BROWSER_TYPE": "chromium",
        "BROWSER_WS_ENDPOINT": "ws://localhost:9222"
      }
    }
  }
}
"@

$configPath = "$mcpDir\mcp-config.json"
$configContent | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "MCP配置文件已创建: $configPath" -ForegroundColor Green

# 创建Edge启动脚本
$edgeScript = @"
@echo off
echo 关闭现有Edge进程...
taskkill /F /IM msedge.exe >nul 2>&1
timeout /t 2 >nul

echo 清理临时目录...
if exist "%TEMP%\edge-debug" rmdir /s /q "%TEMP%\edge-debug"

echo 启动Edge浏览器（调试模式）...
echo 调试端口: http://localhost:9222
start "" "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\edge-debug" --disable-web-security --disable-features=VizDisplayCompositor --no-first-run --no-default-browser-check

echo 等待Edge启动...
timeout /t 5 >nul

echo 测试调试端口...
curl -s http://localhost:9222/json >nul
if %errorlevel% equ 0 (
    echo 成功！Edge调试模式已启动
    echo 请访问: http://localhost:9222
) else (
    echo 警告：调试端口可能未正确启动
    echo 请手动检查 http://localhost:9222
)
pause
"@

$edgeScriptPath = "$mcpDir\start-edge-debug.bat"
$edgeScript | Out-File -FilePath $edgeScriptPath -Encoding ASCII
Write-Host "Edge调试启动脚本已创建: $edgeScriptPath" -ForegroundColor Green

# 创建PowerShell版本的启动脚本
$edgePSScript = @"
# Edge调试模式启动脚本 (PowerShell版本)
Write-Host "关闭现有Edge进程..." -ForegroundColor Yellow
Get-Process -Name "msedge" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

Write-Host "清理临时目录..." -ForegroundColor Yellow
`$tempDir = "`$env:TEMP\edge-debug"
if (Test-Path `$tempDir) {
    Remove-Item `$tempDir -Recurse -Force
}

Write-Host "启动Edge调试模式..." -ForegroundColor Yellow
`$edgePath = "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
`$arguments = @(
    "--remote-debugging-port=9222",
    "--user-data-dir=`$tempDir",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--no-first-run",
    "--no-default-browser-check"
)

Start-Process -FilePath `$edgePath -ArgumentList `$arguments
Start-Sleep -Seconds 5

Write-Host "测试调试端口..." -ForegroundColor Yellow
try {
    `$response = Invoke-WebRequest -Uri "http://localhost:9222/json" -TimeoutSec 5
    Write-Host "✓ Edge调试模式启动成功！" -ForegroundColor Green
    Write-Host "调试页面: http://localhost:9222" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 调试端口测试失败" -ForegroundColor Red
    Write-Host "错误: `$(`$_.Exception.Message)" -ForegroundColor Red
}

Read-Host "按Enter键继续..."
"@

$edgePSScriptPath = "$mcpDir\start-edge-debug.ps1"
$edgePSScript | Out-File -FilePath $edgePSScriptPath -Encoding UTF8
Write-Host "Edge调试启动脚本(PowerShell)已创建: $edgePSScriptPath" -ForegroundColor Green

Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 运行 start-edge-debug.bat 启动Edge调试模式" -ForegroundColor White
Write-Host "2. 访问 http://localhost:9222 验证调试端口" -ForegroundColor White
Write-Host "3. 运行测试脚本验证MCP连接" -ForegroundColor White
