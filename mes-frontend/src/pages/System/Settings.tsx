import React, { useState } from 'react';
import {
  Typography,
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Tabs,
  Row,
  Col,
  Select,
  TimePicker,
  message,
  Divider,
  Upload,
  Avatar,
  Tag,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  NotificationOutlined,
  DatabaseOutlined,
  CloudOutlined,
} from '@ant-design/icons';
import { systemService } from '../../services/business';
import { useApi } from '../../hooks/useApi';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings: React.FC = () => {
  const [generalForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 获取系统设置
  const {
    data: settings,
    loading: settingsLoading,
    refresh: refreshSettings,
  } = useApi(() => systemService.getSettings(), { immediate: true });

  // 模拟系统设置数据
  const mockSettings = {
    general: {
      system_name: 'MES制造执行系统',
      company_name: '智能制造有限公司',
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      date_format: 'YYYY-MM-DD',
      time_format: 'HH:mm:ss',
      currency: 'CNY',
      work_hours_start: dayjs('08:00', 'HH:mm'),
      work_hours_end: dayjs('18:00', 'HH:mm'),
      enable_maintenance: false,
    },
    security: {
      password_min_length: 8,
      password_require_uppercase: true,
      password_require_lowercase: true,
      password_require_numbers: true,
      password_require_symbols: false,
      session_timeout: 30,
      max_login_attempts: 5,
      enable_two_factor: false,
      enable_audit_log: true,
    },
    notification: {
      enable_email: true,
      enable_sms: false,
      enable_push: true,
      email_server: 'smtp.example.com',
      email_port: 587,
      email_username: '<EMAIL>',
      email_use_ssl: true,
      notification_frequency: 'immediate',
    },
    backup: {
      enable_auto_backup: true,
      backup_frequency: 'daily',
      backup_time: dayjs('02:00', 'HH:mm'),
      backup_retention_days: 30,
      backup_location: '/backup',
    },
  };

  const currentSettings = settings || mockSettings;

  const handleGeneralSubmit = async (values: any) => {
    try {
      setLoading(true);
      // await systemService.updateGeneralSettings(values);
      message.success('基本设置保存成功');
    } catch (error) {
      message.error('基本设置保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSecuritySubmit = async (values: any) => {
    try {
      setLoading(true);
      // await systemService.updateSecuritySettings(values);
      message.success('安全设置保存成功');
    } catch (error) {
      message.error('安全设置保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationSubmit = async (values: any) => {
    try {
      setLoading(true);
      // await systemService.updateNotificationSettings(values);
      message.success('通知设置保存成功');
    } catch (error) {
      message.error('通知设置保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    try {
      // await systemService.testEmailSettings();
      message.success('测试邮件发送成功');
    } catch (error) {
      message.error('测试邮件发送失败');
    }
  };

  const handleBackupNow = async () => {
    try {
      // await systemService.createBackup();
      message.success('备份创建成功');
    } catch (error) {
      message.error('备份创建失败');
    }
  };

  const renderGeneralSettings = () => (
    <Card title="基本设置" size="small">
      <Form
        form={generalForm}
        layout="vertical"
        initialValues={currentSettings.general}
        onFinish={handleGeneralSubmit}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="system_name"
              label="系统名称"
              rules={[{ required: true, message: '请输入系统名称' }]}
            >
              <Input placeholder="请输入系统名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="company_name"
              label="公司名称"
              rules={[{ required: true, message: '请输入公司名称' }]}
            >
              <Input placeholder="请输入公司名称" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="timezone"
              label="时区"
              rules={[{ required: true, message: '请选择时区' }]}
            >
              <Select placeholder="请选择时区">
                <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
                <Option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</Option>
                <Option value="America/New_York">America/New_York (UTC-5)</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="language"
              label="语言"
              rules={[{ required: true, message: '请选择语言' }]}
            >
              <Select placeholder="请选择语言">
                <Option value="zh-CN">简体中文</Option>
                <Option value="en-US">English</Option>
                <Option value="ja-JP">日本語</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="currency"
              label="货币"
              rules={[{ required: true, message: '请选择货币' }]}
            >
              <Select placeholder="请选择货币">
                <Option value="CNY">人民币 (CNY)</Option>
                <Option value="USD">美元 (USD)</Option>
                <Option value="EUR">欧元 (EUR)</Option>
                <Option value="JPY">日元 (JPY)</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="date_format"
              label="日期格式"
            >
              <Select placeholder="请选择日期格式">
                <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
                <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="work_hours_start"
              label="工作开始时间"
            >
              <TimePicker format="HH:mm" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="work_hours_end"
              label="工作结束时间"
            >
              <TimePicker format="HH:mm" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="enable_maintenance"
          label="维护模式"
          valuePropName="checked"
        >
          <Switch checkedChildren="开启" unCheckedChildren="关闭" />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              保存设置
            </Button>
            <Button icon={<ReloadOutlined />} onClick={() => generalForm.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  const renderSecuritySettings = () => (
    <Card title="安全设置" size="small">
      <Form
        form={securityForm}
        layout="vertical"
        initialValues={currentSettings.security}
        onFinish={handleSecuritySubmit}
      >
        <Divider orientation="left">密码策略</Divider>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="password_min_length"
              label="最小密码长度"
              rules={[{ required: true, message: '请输入最小密码长度' }]}
            >
              <InputNumber min={6} max={20} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="session_timeout"
              label="会话超时时间(分钟)"
              rules={[{ required: true, message: '请输入会话超时时间' }]}
            >
              <InputNumber min={5} max={480} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="max_login_attempts"
              label="最大登录尝试次数"
              rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
            >
              <InputNumber min={3} max={10} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              name="password_require_uppercase"
              label="要求大写字母"
              valuePropName="checked"
            >
              <Switch size="small" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="password_require_lowercase"
              label="要求小写字母"
              valuePropName="checked"
            >
              <Switch size="small" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="password_require_numbers"
              label="要求数字"
              valuePropName="checked"
            >
              <Switch size="small" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="password_require_symbols"
              label="要求特殊字符"
              valuePropName="checked"
            >
              <Switch size="small" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">安全功能</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="enable_two_factor"
              label="启用双因子认证"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="enable_audit_log"
              label="启用审计日志"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              保存设置
            </Button>
            <Button icon={<ReloadOutlined />} onClick={() => securityForm.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px' 
      }}>
        <Title level={2} style={{ margin: 0 }}>
          系统设置
        </Title>
        <Space>
          <Button 
            icon={<ReloadOutlined />}
            onClick={refreshSettings}
            loading={settingsLoading}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Tabs
        defaultActiveKey="general"
        items={[
          {
            key: 'general',
            label: (
              <span>
                <SettingOutlined />
                基本设置
              </span>
            ),
            children: renderGeneralSettings(),
          },
          {
            key: 'security',
            label: (
              <span>
                <SecurityScanOutlined />
                安全设置
              </span>
            ),
            children: renderSecuritySettings(),
          },
          {
            key: 'notification',
            label: (
              <span>
                <NotificationOutlined />
                通知设置
              </span>
            ),
            children: (
              <Card title="通知设置" size="small">
                <div style={{ padding: '40px', textAlign: 'center' }}>
                  <Text type="secondary">通知设置功能开发中...</Text>
                </div>
              </Card>
            ),
          },
          {
            key: 'backup',
            label: (
              <span>
                <DatabaseOutlined />
                备份设置
              </span>
            ),
            children: (
              <Card title="备份设置" size="small">
                <div style={{ padding: '40px', textAlign: 'center' }}>
                  <Text type="secondary">备份设置功能开发中...</Text>
                </div>
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

export default Settings;
