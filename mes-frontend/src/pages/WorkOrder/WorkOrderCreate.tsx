import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  Steps,
  Row,
  Col,
  Table,
  message,
  Divider,
  Tag,
  Tooltip,
  Modal,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { workOrderService, projectService, partService } from '../../services/business';
import { Project, Part, WorkOrder } from '../../types';
import { useApi } from '../../hooks/useApi';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const WorkOrderCreate: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [routingSteps, setRoutingSteps] = useState<any[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // 获取项目列表
  const { data: projects } = useApi(() => projectService.getProjects({ page: 1, limit: 100 }));
  
  // 获取零件列表
  const { data: parts } = useApi(() => partService.getParts({ page: 1, limit: 100 }));

  // 模拟数据
  const mockProjects: Project[] = [
    {
      id: 1,
      name: '汽车零部件生产项目',
      description: '高精度汽车零部件批量生产',
      status: 'ACTIVE',
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      name: '电子产品外壳项目',
      description: '消费电子产品外壳制造',
      status: 'ACTIVE',
      start_date: '2024-02-01',
      end_date: '2024-11-30',
      created_at: '2024-02-01T00:00:00Z',
      updated_at: '2024-02-01T00:00:00Z',
    },
  ];

  const mockParts: Part[] = [
    {
      id: 1,
      part_number: 'P001',
      name: '发动机支架',
      description: '汽车发动机支撑部件',
      category: '机械零件',
      unit: '个',
      standard_cost: 150.00,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      part_number: 'P002',
      name: '手机外壳',
      description: '智能手机铝合金外壳',
      category: '外壳',
      unit: '个',
      standard_cost: 25.00,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  const displayProjects = projects?.list || mockProjects;
  const displayParts = parts?.list || mockParts;

  // 模拟工艺路线
  const mockRoutingSteps = [
    { id: 1, step_number: 1, process_name: '原料准备', standard_hours: 2, description: '准备生产原料' },
    { id: 2, step_number: 2, process_name: 'CNC加工', standard_hours: 4, description: '数控机床精密加工' },
    { id: 3, step_number: 3, process_name: '质量检测', standard_hours: 1, description: '产品质量检验' },
    { id: 4, step_number: 4, process_name: '表面处理', standard_hours: 3, description: '表面喷涂处理' },
    { id: 5, step_number: 5, process_name: '包装入库', standard_hours: 1, description: '产品包装和入库' },
  ];

  const handleProjectChange = (projectId: number) => {
    const project = displayProjects.find(p => p.id === projectId);
    setSelectedProject(project || null);
  };

  const handlePartChange = (partId: number) => {
    const part = displayParts.find(p => p.id === partId);
    setSelectedPart(part || null);
    // 模拟获取工艺路线
    setRoutingSteps(mockRoutingSteps);
  };

  const handleNext = async () => {
    try {
      await form.validateFields();
      setCurrentStep(currentStep + 1);
    } catch (error) {
      message.error('请完善当前步骤的信息');
    }
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const handlePreview = () => {
    form.validateFields().then(() => {
      setPreviewVisible(true);
    }).catch(() => {
      message.error('请完善工单信息');
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const workOrderData = {
        ...values,
        project_id: selectedProject?.id,
        part_id: selectedPart?.id,
        planned_start: values.planned_start?.format('YYYY-MM-DD HH:mm:ss'),
        planned_end: values.planned_end?.format('YYYY-MM-DD HH:mm:ss'),
        routing_steps: routingSteps,
      };

      // 这里应该调用API创建工单
      // await workOrderService.createWorkOrder(workOrderData);
      
      message.success('工单创建成功');
      navigate('/work-orders');
    } catch (error) {
      message.error('工单创建失败');
    } finally {
      setLoading(false);
    }
  };

  const routingColumns = [
    {
      title: '步骤',
      dataIndex: 'step_number',
      key: 'step_number',
      width: 80,
    },
    {
      title: '工序名称',
      dataIndex: 'process_name',
      key: 'process_name',
    },
    {
      title: '标准工时',
      dataIndex: 'standard_hours',
      key: 'standard_hours',
      render: (hours: number) => `${hours}h`,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  const steps = [
    {
      title: '基本信息',
      description: '工单基础信息',
    },
    {
      title: '生产计划',
      description: '生产数量和时间',
    },
    {
      title: '工艺路线',
      description: '生产工序安排',
    },
    {
      title: '确认创建',
      description: '预览并创建工单',
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title="基本信息" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="project_id"
                  label="所属项目"
                  rules={[{ required: true, message: '请选择项目' }]}
                >
                  <Select
                    placeholder="请选择项目"
                    onChange={handleProjectChange}
                    showSearch
                    filterOption={(input, option) =>
                      option?.children?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {displayProjects.map(project => (
                      <Option key={project.id} value={project.id}>
                        {project.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="part_id"
                  label="生产零件"
                  rules={[{ required: true, message: '请选择零件' }]}
                >
                  <Select
                    placeholder="请选择零件"
                    onChange={handlePartChange}
                    showSearch
                    filterOption={(input, option) =>
                      option?.children?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {displayParts.map(part => (
                      <Option key={part.id} value={part.id}>
                        {part.part_number} - {part.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label="优先级"
                  rules={[{ required: true, message: '请选择优先级' }]}
                >
                  <Select placeholder="请选择优先级">
                    <Option value="LOW">低</Option>
                    <Option value="MEDIUM">中</Option>
                    <Option value="HIGH">高</Option>
                    <Option value="URGENT">紧急</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="customer"
                  label="客户"
                >
                  <Input placeholder="请输入客户名称" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="工单描述"
            >
              <TextArea rows={3} placeholder="请输入工单描述" />
            </Form.Item>

            {selectedProject && (
              <div style={{ marginTop: '16px', padding: '12px', background: '#f5f5f5', borderRadius: '6px' }}>
                <Text strong>项目信息：</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>{selectedProject.name}</Text>
                  <br />
                  <Text type="secondary">{selectedProject.description}</Text>
                </div>
              </div>
            )}

            {selectedPart && (
              <div style={{ marginTop: '16px', padding: '12px', background: '#f5f5f5', borderRadius: '6px' }}>
                <Text strong>零件信息：</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text>{selectedPart.part_number} - {selectedPart.name}</Text>
                  <br />
                  <Text type="secondary">{selectedPart.description}</Text>
                  <br />
                  <Text type="secondary">标准成本: ¥{selectedPart.standard_cost}</Text>
                </div>
              </div>
            )}
          </Card>
        );

      case 1:
        return (
          <Card title="生产计划" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="quantity"
                  label="生产数量"
                  rules={[{ required: true, message: '请输入生产数量' }]}
                >
                  <InputNumber
                    min={1}
                    style={{ width: '100%' }}
                    placeholder="请输入数量"
                    addonAfter={selectedPart?.unit || '个'}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="planned_start"
                  label="计划开始时间"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <DatePicker
                    showTime
                    style={{ width: '100%' }}
                    placeholder="选择开始时间"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="planned_end"
                  label="计划结束时间"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <DatePicker
                    showTime
                    style={{ width: '100%' }}
                    placeholder="选择结束时间"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="生产备注"
            >
              <TextArea rows={3} placeholder="请输入生产相关备注" />
            </Form.Item>
          </Card>
        );

      case 2:
        return (
          <Card title="工艺路线" size="small">
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary">
                <InfoCircleOutlined style={{ marginRight: '4px' }} />
                以下是该零件的标准工艺路线，您可以根据需要进行调整
              </Text>
            </div>
            
            <Table
              columns={routingColumns}
              dataSource={routingSteps}
              rowKey="id"
              pagination={false}
              size="small"
            />

            <div style={{ marginTop: '16px', textAlign: 'center' }}>
              <Text type="secondary">
                总工时: {routingSteps.reduce((sum, step) => sum + step.standard_hours, 0)} 小时
              </Text>
            </div>
          </Card>
        );

      case 3:
        return (
          <Card title="工单预览" size="small">
            <div style={{ padding: '16px' }}>
              <Text strong style={{ fontSize: '16px' }}>工单信息确认</Text>
              <Divider />
              
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>项目：</Text>
                  <div>{selectedProject?.name}</div>
                </Col>
                <Col span={12}>
                  <Text strong>零件：</Text>
                  <div>{selectedPart?.part_number} - {selectedPart?.name}</div>
                </Col>
                <Col span={12}>
                  <Text strong>生产数量：</Text>
                  <div>{form.getFieldValue('quantity')} {selectedPart?.unit}</div>
                </Col>
                <Col span={12}>
                  <Text strong>优先级：</Text>
                  <div>
                    <Tag color={
                      form.getFieldValue('priority') === 'URGENT' ? 'red' :
                      form.getFieldValue('priority') === 'HIGH' ? 'orange' :
                      form.getFieldValue('priority') === 'MEDIUM' ? 'blue' : 'default'
                    }>
                      {form.getFieldValue('priority')}
                    </Tag>
                  </div>
                </Col>
                <Col span={12}>
                  <Text strong>计划开始：</Text>
                  <div>{form.getFieldValue('planned_start')?.format('YYYY-MM-DD HH:mm')}</div>
                </Col>
                <Col span={12}>
                  <Text strong>计划结束：</Text>
                  <div>{form.getFieldValue('planned_end')?.format('YYYY-MM-DD HH:mm')}</div>
                </Col>
              </Row>

              <Divider />
              <Text strong>工艺路线：</Text>
              <div style={{ marginTop: '8px' }}>
                {routingSteps.map((step, index) => (
                  <Tag key={step.id} style={{ marginBottom: '4px' }}>
                    {step.step_number}. {step.process_name} ({step.standard_hours}h)
                  </Tag>
                ))}
              </div>
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px' 
      }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/work-orders')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            创建工单
          </Title>
        </Space>
      </div>

      <Card>
        <Steps current={currentStep} style={{ marginBottom: '32px' }}>
          {steps.map(step => (
            <Step key={step.title} title={step.title} description={step.description} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            priority: 'MEDIUM',
            planned_start: dayjs().add(1, 'day'),
            planned_end: dayjs().add(7, 'day'),
          }}
        >
          {renderStepContent()}
        </Form>

        <div style={{ marginTop: '24px', textAlign: 'right' }}>
          <Space>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <>
                <Button onClick={handlePreview}>
                  预览
                </Button>
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />}
                  loading={loading}
                  onClick={handleSubmit}
                >
                  创建工单
                </Button>
              </>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default WorkOrderCreate;
