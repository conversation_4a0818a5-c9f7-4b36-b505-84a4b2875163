# 浏览器MCP设置指南

## 环境说明
- Ubuntu服务器：运行MES系统后端
- Windows客户端：VS Code远程连接 + Edge浏览器
- 目标：在Windows Edge中使用MCP进行浏览器自动化

## 方案1：使用现有MCP工具（推荐）
我已经有浏览器MCP工具，可以直接用于：
- 自动化测试您的MES系统
- API端点验证
- UI功能测试

## 方案2：Windows本地MCP设置

### 1. 安装Node.js（Windows）
```powershell
# 下载并安装Node.js LTS版本
# https://nodejs.org/
```

### 2. 安装浏览器MCP服务器
```powershell
# 安装MCP浏览器服务器
npm install -g @modelcontextprotocol/server-browser

# 或者使用yarn
yarn global add @modelcontextprotocol/server-browser
```

### 3. 配置Edge浏览器
```powershell
# 启动Edge时添加调试端口
"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" --remote-debugging-port=9222
```

### 4. 创建MCP配置文件
创建 `mcp-config.json`:
```json
{
  "mcpServers": {
    "browser": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-browser"],
      "env": {
        "BROWSER_WS_ENDPOINT": "ws://localhost:9222"
      }
    }
  }
}
```

### 5. VS Code配置
在VS Code中安装MCP扩展并配置连接。

## 方案3：使用现有工具测试您的MES系统

我可以立即使用现有的浏览器工具来：

1. **启动您的MES系统**
2. **自动化测试前端界面**
3. **验证API功能**
4. **生成测试报告**

## 推荐操作
建议先使用方案3，我可以立即帮您：
1. 启动MES系统
2. 进行自动化测试
3. 验证系统功能

您希望我现在就开始测试您的MES系统吗？
