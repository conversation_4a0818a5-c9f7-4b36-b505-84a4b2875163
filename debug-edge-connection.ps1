# Edge调试连接诊断脚本
Write-Host "=== Edge调试连接诊断 ===" -ForegroundColor Green

# 1. 检查Edge安装
Write-Host "`n1. 检查Edge安装..." -ForegroundColor Yellow
$edgePaths = @(
    "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
    "C:\Program Files\Microsoft\Edge\Application\msedge.exe"
)

$edgePath = $null
foreach ($path in $edgePaths) {
    if (Test-Path $path) {
        $edgePath = $path
        Write-Host "✓ 找到Edge: $path" -ForegroundColor Green
        break
    }
}

if (-not $edgePath) {
    Write-Host "✗ 未找到Edge安装" -ForegroundColor Red
    exit 1
}

# 2. 检查当前Edge进程
Write-Host "`n2. 检查当前Edge进程..." -ForegroundColor Yellow
$edgeProcesses = Get-Process -Name "msedge" -ErrorAction SilentlyContinue
if ($edgeProcesses) {
    Write-Host "发现 $($edgeProcesses.Count) 个Edge进程:" -ForegroundColor Yellow
    $edgeProcesses | ForEach-Object {
        Write-Host "  PID: $($_.Id) - $($_.ProcessName)" -ForegroundColor White
    }
    
    Write-Host "关闭所有Edge进程..." -ForegroundColor Yellow
    $edgeProcesses | Stop-Process -Force
    Start-Sleep -Seconds 3
} else {
    Write-Host "✓ 没有运行中的Edge进程" -ForegroundColor Green
}

# 3. 检查端口占用
Write-Host "`n3. 检查端口9222占用..." -ForegroundColor Yellow
$portCheck = netstat -ano | Select-String ":9222"
if ($portCheck) {
    Write-Host "端口9222被占用:" -ForegroundColor Red
    $portCheck | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    
    # 尝试释放端口
    $portCheck | ForEach-Object {
        $line = $_.ToString()
        if ($line -match "(\d+)$") {
            $pid = $matches[1]
            Write-Host "尝试终止进程 PID: $pid" -ForegroundColor Yellow
            try {
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            } catch {
                Write-Host "无法终止进程 $pid" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "✓ 端口9222空闲" -ForegroundColor Green
}

# 4. 清理临时目录
Write-Host "`n4. 清理临时目录..." -ForegroundColor Yellow
$tempDir = "$env:TEMP\edge-debug"
if (Test-Path $tempDir) {
    try {
        Remove-Item $tempDir -Recurse -Force
        Write-Host "✓ 临时目录已清理" -ForegroundColor Green
    } catch {
        Write-Host "✗ 无法清理临时目录: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✓ 临时目录不存在" -ForegroundColor Green
}

# 5. 启动Edge调试模式
Write-Host "`n5. 启动Edge调试模式..." -ForegroundColor Yellow
$arguments = @(
    "--remote-debugging-port=9222",
    "--user-data-dir=$tempDir",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-extensions",
    "--disable-plugins"
)

Write-Host "启动命令: $edgePath" -ForegroundColor Cyan
Write-Host "参数: $($arguments -join ' ')" -ForegroundColor Cyan

try {
    $process = Start-Process -FilePath $edgePath -ArgumentList $arguments -PassThru
    Write-Host "✓ Edge进程已启动 (PID: $($process.Id))" -ForegroundColor Green
} catch {
    Write-Host "✗ 启动Edge失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 6. 等待并测试连接
Write-Host "`n6. 等待Edge启动并测试连接..." -ForegroundColor Yellow
for ($i = 1; $i -le 10; $i++) {
    Write-Host "尝试 $i/10..." -ForegroundColor White
    Start-Sleep -Seconds 2
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:9222/json" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ 调试端口连接成功！" -ForegroundColor Green
            
            # 解析响应
            $tabs = $response.Content | ConvertFrom-Json
            Write-Host "发现 $($tabs.Count) 个标签页:" -ForegroundColor Green
            $tabs | ForEach-Object {
                Write-Host "  - $($_.title) ($($_.url))" -ForegroundColor White
            }
            
            Write-Host "`n=== 成功！===" -ForegroundColor Green
            Write-Host "调试页面: http://localhost:9222" -ForegroundColor Cyan
            Write-Host "WebSocket端点: $($tabs[0].webSocketDebuggerUrl)" -ForegroundColor Cyan
            
            # 自动打开调试页面
            Start-Process "http://localhost:9222"
            
            break
        }
    } catch {
        if ($i -eq 10) {
            Write-Host "✗ 调试端口连接失败" -ForegroundColor Red
            Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
            
            # 提供故障排除建议
            Write-Host "`n=== 故障排除建议 ===" -ForegroundColor Yellow
            Write-Host "1. 检查防火墙设置" -ForegroundColor White
            Write-Host "2. 尝试以管理员身份运行" -ForegroundColor White
            Write-Host "3. 检查Edge版本是否支持调试" -ForegroundColor White
            Write-Host "4. 尝试使用不同的端口 (如9223)" -ForegroundColor White
        }
    }
}

Write-Host "`n按Enter键退出..." -ForegroundColor Gray
Read-Host
