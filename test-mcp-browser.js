// MCP浏览器连接测试脚本
const { spawn } = require('child_process');
const http = require('http');

console.log('=== MCP浏览器连接测试 ===');

// 测试Edge调试端口连接
function testEdgeConnection() {
    return new Promise((resolve, reject) => {
        console.log('测试Edge调试端口连接...');
        
        const req = http.get('http://localhost:9222/json', (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const tabs = JSON.parse(data);
                    console.log(`✓ Edge调试端口连接成功，发现 ${tabs.length} 个标签页`);
                    resolve(tabs);
                } catch (error) {
                    reject(new Error('解析调试信息失败: ' + error.message));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error('无法连接到Edge调试端口: ' + error.message));
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('连接超时'));
        });
    });
}

// 测试MCP服务器
function testMCPServer() {
    return new Promise((resolve, reject) => {
        console.log('启动MCP服务器测试...');
        
        const mcpServer = spawn('node', [
            './node_modules/@modelcontextprotocol/server-browser/dist/index.js'
        ], {
            env: {
                ...process.env,
                BROWSER_TYPE: 'chromium',
                BROWSER_WS_ENDPOINT: 'ws://localhost:9222'
            }
        });
        
        let output = '';
        mcpServer.stdout.on('data', (data) => {
            output += data.toString();
            console.log('MCP输出:', data.toString().trim());
        });
        
        mcpServer.stderr.on('data', (data) => {
            console.log('MCP错误:', data.toString().trim());
        });
        
        // 5秒后检查状态
        setTimeout(() => {
            if (!mcpServer.killed) {
                console.log('✓ MCP服务器启动成功');
                mcpServer.kill();
                resolve(true);
            } else {
                reject(new Error('MCP服务器启动失败'));
            }
        }, 5000);
        
        mcpServer.on('error', (error) => {
            reject(new Error('MCP服务器错误: ' + error.message));
        });
    });
}

// 创建简单的MES系统测试
function createMESTest() {
    const testHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>MES系统测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-button { padding: 10px 20px; margin: 10px; background: #1890ff; color: white; border: none; cursor: pointer; }
        .test-result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MES系统测试页面</h1>
        <button class="test-button" onclick="testAPI()">测试API连接</button>
        <button class="test-button" onclick="testDatabase()">测试数据库</button>
        <button class="test-button" onclick="testWorkflow()">测试工作流</button>
        <div id="results"></div>
    </div>
    
    <script>
        function testAPI() {
            document.getElementById('results').innerHTML += 
                '<div class="test-result">API测试: 连接到后端服务器...</div>';
        }
        
        function testDatabase() {
            document.getElementById('results').innerHTML += 
                '<div class="test-result">数据库测试: 查询生产数据...</div>';
        }
        
        function testWorkflow() {
            document.getElementById('results').innerHTML += 
                '<div class="test-result">工作流测试: 模拟生产流程...</div>';
        }
    </script>
</body>
</html>`;
    
    require('fs').writeFileSync('./mes-test.html', testHTML);
    console.log('✓ MES测试页面已创建: mes-test.html');
}

// 主测试流程
async function runTests() {
    try {
        // 测试Edge连接
        await testEdgeConnection();
        
        // 测试MCP服务器
        await testMCPServer();
        
        // 创建测试页面
        createMESTest();
        
        console.log('\n=== 测试完成 ===');
        console.log('✓ 所有测试通过');
        console.log('\n下一步:');
        console.log('1. 在Edge中打开 mes-test.html');
        console.log('2. 使用MCP工具与页面交互');
        console.log('3. 测试您的MES系统');
        
    } catch (error) {
        console.error('✗ 测试失败:', error.message);
        console.log('\n故障排除:');
        console.log('1. 确保Edge以调试模式启动');
        console.log('2. 检查端口9222是否被占用');
        console.log('3. 验证MCP依赖包安装');
    }
}

// 运行测试
runTests();
